<!-- 能源总览 -->
<template>
  <CPanel>
    <template #header>能耗总览</template>
    <template #content>
      <div class="energy-overview">
        <!-- 上半部分：圆环图和系统列表 -->
        <div class="energy-summary">
          <!-- 左侧圆环图 -->
          <div class="energy-chart">
            <CEcharts :option="pieOption" />
            <div class="chart-center">
              <div class="center-number">{{ energyData?.total || '0' }}</div>
              <div class="center-text">能耗总类</div>
            </div>
          </div>

          <!-- 右侧系统列表 -->
          <div class="system-list">
            <div class="system-item" v-for="item in systemData" :key="item.name">
              <div class="system-indicator" :style="{ backgroundColor: item.color }"></div>
              <span class="system-name">{{ item.name }}</span>
              <div class="system-value-container">
                <span class="system-value">{{ item.value }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 下半部分：能耗趋势图 -->
        <div class="energy-trend">
          <!-- 背景图片容器 -->
          <div class="trend-background">
            <!-- 标题和选择器覆盖在背景图上 -->
            <div class="trend-header">
              <div class="trend-title">
                <span>能耗趋势</span>
              </div>
              <div class="trend-selector">
                <select class="time-selector" :value="currentDimension" @change="handleDimensionChange">
                  <option value="day">日</option>
                  <option value="month">月</option>
                  <option value="year">年</option>
                </select>
              </div>
            </div>
            <!-- 图表区域 -->
            <div class="trend-chart">
              <CEcharts :option="trendOption" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import { ref, onMounted } from 'vue'
import { getEnergyOverview } from '@/api'

// 数据类型定义
interface PieChartVO {
  type: string[]
  data: string[]
}

interface ChartVO {
  type: string[]
  axis: string[]
  data: string[][]
}

interface CabinEnergyVO {
  total: string
  pieChartVO: PieChartVO
  trend: ChartVO
}

// 响应式数据
const energyData = ref<CabinEnergyVO | null>(null)
const currentDimension = ref('day')
const systemData = ref<Array<{ name: string; value: string; color: string }>>([])

// 颜色配置
const systemColors = ['#66EBB5', '#FFD966', '#A1F1FF', '#FF6B6B', '#4ECDC4', '#45B7D1']

// 圆环图配置
const pieOption = ref<any>({})

// 趋势图配置
const trendOption = ref<any>({})

// 初始化圆环图
const initPieChart = () => {
  if (!energyData.value?.pieChartVO) {
    return {}
  }

  const { type, data } = energyData.value.pieChartVO
  const chartData = type.map((name, index) => ({
    value: parseFloat(data[index]),
    name,
    itemStyle: { color: systemColors[index] || '#4A5568' }
  }))

  return {
    tooltip: {
      show: false
    },
    legend: {
      show: false
    },
    series: [
      {
        type: 'pie',
        radius: ['65%', '75%'],
        center: ['50%', '50%'],
        data: chartData,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        emphasis: {
          disabled: true
        },
        itemStyle: {
          borderWidth: 2,
          borderColor: 'transparent'
        }
      }
    ]
  }
}

// 初始化趋势图
const initTrendChart = () => {
  if (!energyData.value?.trend) {
    return {}
  }

  const { axis, data } = energyData.value.trend
  const trendData = data[0]?.map(value => parseFloat(value)) || []

  // 计算Y轴最大值
  const maxValue = Math.max(...trendData) * 1.2

  return {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#00D4AA',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      data: ['能耗'],
      right: '10%',
      top: '5%',
      textStyle: {
        color: '#C5D6E6'
      },
      itemWidth: 15,
      itemHeight: 2
    },
    grid: {
      left: '0',
      right: '5%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: axis,
      axisLabel: {
        color: '#C5D6E6',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(76, 93, 130, 0.5)'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: maxValue,
      axisLabel: {
        color: '#C5D6E6',
        fontSize: 12
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(76, 93, 130, 0.3)',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '能耗',
        type: 'line',
        data: trendData,
        smooth: true,
        lineStyle: {
          color: '#00D4AA',
          width: 3
        },
        itemStyle: {
          color: '#00D4AA'
        },
        symbol: 'none'
      }
    ]
  }
}

// 获取能耗数据
const fetchEnergyData = async (dimension: string = 'day') => {  
  try {
    const response = await getEnergyOverview(dimension)
    console.log('获取能耗数据成功:', response.data)
    if ( response.data&&response.data.data) {
      energyData.value = response.data.data

      // 更新系统数据列表
      if (response.data.data.pieChartVO) {
        systemData.value = response.data.data.pieChartVO.type.map((name: string, index: number) => ({
          name,
          value: response.data.data.pieChartVO.data[index],
          color: systemColors[index] || '#4A5568'
        }))
      }

      // 更新图表
      pieOption.value = initPieChart()
      trendOption.value = initTrendChart()
    }
  } catch (error) {
    console.error('获取能耗数据失败:', error)
  }
}

// 处理时间维度变化
const handleDimensionChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  currentDimension.value = target.value
  fetchEnergyData(target.value)
}

onMounted(() => {
  fetchEnergyData(currentDimension.value)
})
</script>

<style lang="scss" scoped>
.energy-overview {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: url('@/assets/img/ny_bg.png') no-repeat center center;
  background-size: 100% 100%;
}

.energy-summary {
  display: flex;
  align-items: center;
  gap: 20px;
  height: 180px;
}

.energy-chart {
  position: relative;
  width: 160px;
  height: 160px;
  flex-shrink: 0;
}

.chart-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;

  .center-number {
    font-size: 36px;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 4px;
  }

  .center-text {
    font-size: 14px;
    color: #C5D6E6;
  }
}

.system-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-right: 10px;
}

.system-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border-left: 3px solid transparent;

  &:nth-child(1) {
    border-left-color: #66EBB5;
  }

  &:nth-child(2) {
    border-left-color: #FFD966;
  }

  &:nth-child(3) {
    border-left-color: #A1F1FF;
  }
}

.system-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.system-name {
  flex: 1;
  color: #C5D6E6;
  font-size: 14px;
}

.system-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.system-value {
  color: #fff;
  font-size: 16px;
  font-weight: bold;
}

.system-badge {
  background: url('@/assets/img/circle_bg.svg') no-repeat center center;
  background-size: 100% 100%;
  padding: 2px 8px;
  font-size: 12px;
  color: #00D4AA;
  border-radius: 10px;
}

.energy-trend {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.trend-background {
  position: relative;
  flex: 1;

  display: flex;
  flex-direction: column;
}

.trend-header {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: url('@/assets/img/n_samllTitle.png') no-repeat center top;
  background-size: 100% 100%;
  height: 40px;
  padding: 0 20px;
}

.trend-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  height: 40px;
}

.trend-icon {
  color: #00D4AA;
  font-size: 12px;
}

.trend-selector {
  .time-selector {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: #C5D6E6;
    padding: 4px 12px;
    font-size: 12px;
    outline: none;
    cursor: pointer;

    &:hover {
      background: rgba(255, 255, 255, 0.15);
    }

    option {
      background: #1a1a1a;
      color: #C5D6E6;
    }
  }
}

.trend-chart {
  flex: 1;
  min-height: 200px;
  padding-top: 50px;
  /* 为标题留出空间 */
  padding: 50px 10px 10px 10px;
}
</style>
